# LangGraph + MCP 集成示例

这个项目展示了如何使用 `langchain-mcp-adapters` 来简化 LangGraph 与 Model Context Protocol (MCP) 的集成。

## 🎯 主要改进

### ✅ 使用官方适配器
- **之前**: 使用自定义的 `McpToolWrapper` (149 行代码)
- **现在**: 使用 `langchain-mcp-adapters` (官方支持)
- **结果**: 代码大幅简化，更稳定可靠

### 📦 依赖更新
```toml
# 新增依赖
langchain-mcp-adapters = "^0.1.7"

# 保留的 MCP 核心依赖
mcp = "^1.9.4"
```

## 🚀 快速开始

### 1. 安装依赖
```bash
cd agent-py
poetry install
```

### 2. 设置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，设置你的 API 密钥
DS_API_KEY=your_deepseek_api_key
TAVILY_API_KEY=your_tavily_api_key
```

### 3. 测试代理
```bash
# 测试基本功能
python test_agent.py

# 启动 Web 服务
poetry run demo
```

## 🔧 MCP 服务器配置

### 内置 MCP 服务器

#### 数学服务器 (`math_server.py`)
提供以下工具：
- `add(a, b)` - 加法
- `subtract(a, b)` - 减法
- `multiply(a, b)` - 乘法
- `divide(a, b)` - 除法

#### 天气服务器 (Node.js)
使用已测试的天气 MCP 服务器：
- 路径: `D:/mcp-space/mcp-course/examples/mcp-server/dist/fastmcp_server.js`
- 提供天气查询功能

### 添加自定义 MCP 服务器
在 `agent.py` 中的 `get_tools()` 函数里配置：

```python
mcp_client = MultiServerMCPClient({
    "math": {
        "command": "python",
        "args": ["/path/to/math_server.py"],
        "transport": "stdio",
    },
    "weather": {
        "command": "node",
        "args": ["D:/mcp-space/mcp-course/examples/mcp-server/dist/fastmcp_server.js"],
        "transport": "stdio",
    }
})
```

## 📁 项目结构

```
agent-py/
├── tutorial_quickstart/
│   ├── agent.py          # 主要代理逻辑（简化后）
│   └── demo.py           # FastAPI 服务器
├── math_server.py        # 示例 MCP 服务器
├── test_agent.py         # 测试脚本
├── pyproject.toml        # 依赖配置
└── README_MCP.md         # 本文档
```

## 🔍 代码对比

### 之前 (自定义包装器)
- `mcp_tool_wrapper.py`: 149 行复杂的包装器代码
- 手动处理 MCP 协议细节
- 需要维护类型转换和错误处理

### 现在 (官方适配器)
```python
from langchain_mcp_adapters.client import MultiServerMCPClient

# 简单配置
mcp_client = MultiServerMCPClient({...})
tools = await mcp_client.get_tools()

# 直接使用
agent = create_react_agent(llm, tools)
```

## 🎉 优势

1. **代码简化**: 删除了 149 行自定义包装器代码
2. **官方支持**: 使用 LangChain 官方维护的适配器
3. **更好的稳定性**: 官方适配器经过充分测试
4. **自动更新**: 跟随 LangChain 生态系统更新
5. **更多功能**: 支持多种传输协议和高级特性

## 🛠️ 故障排除

### MCP 工具加载失败
如果看到 "成功加载 0 个 MCP 工具"，检查：
1. MCP 服务器路径是否正确
2. Python 环境是否包含 `mcp` 包
3. 服务器脚本是否可执行

### 环境变量问题
确保设置了必要的 API 密钥：
- `DS_API_KEY`: DeepSeek API 密钥
- `TAVILY_API_KEY`: Tavily 搜索 API 密钥

## 📚 相关资源

- [LangChain MCP Adapters](https://github.com/langchain-ai/langchain-mcp-adapters)
- [Model Context Protocol](https://modelcontextprotocol.io/)
- [LangGraph 文档](https://langchain-ai.github.io/langgraph/)

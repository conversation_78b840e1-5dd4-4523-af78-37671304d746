#!/usr/bin/env python3
"""示例数学 MCP 服务器"""

from mcp.server.fastmcp import FastMCP

# 创建 MCP 服务器
mcp = FastMCP("Math")

@mcp.tool()
def add(a: int, b: int) -> int:
    """将两个数字相加"""
    return a + b

@mcp.tool()
def multiply(a: int, b: int) -> int:
    """将两个数字相乘"""
    return a * b

@mcp.tool()
def subtract(a: int, b: int) -> int:
    """从第一个数字中减去第二个数字"""
    return a - b

@mcp.tool()
def divide(a: float, b: float) -> float:
    """将第一个数字除以第二个数字"""
    if b == 0:
        raise ValueError("不能除以零")
    return a / b

if __name__ == "__main__":
    print("启动数学 MCP 服务器...")
    mcp.run(transport="stdio")

[tool.poetry]
name = "tutorial_quickstart"
version = "0.1.0"
description = "Quickstart Tutorial"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"

[project]
name = "tutorial_quickstart"
version = "0.0.1"


[build-system]
requires = ["setuptools >= 61.0"]
build-backend = "setuptools.build_meta"

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
copilotkit = "0.1.41"
langgraph = "0.2.52"
langchain-core = "^0.3.12"
langchain-community = "^0.3.1"
langchain-anthropic = "0.2.3"
langchain = "0.3.4"
openai = "^1.52.1"
tavily-python = "^0.5.0"
python-dotenv = "^1.0.1"
uvicorn = "^0.31.0"
langchain-deepseek = "^0.1.3"

python-docx = "^1.2.0"
# fpdf = "^1.7.2"  # 删除旧依赖
fpdf2 = "^2.7.8"  # 添加新依赖
mcp = "^1.9.4"
[tool.poetry.scripts]
demo = "tutorial_quickstart.demo:main"

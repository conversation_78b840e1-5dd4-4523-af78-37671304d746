#!/usr/bin/env python3
"""测试代理功能"""

import asyncio
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from tutorial_quickstart.agent import initialize_agent

async def test_agent():
    """测试代理功能"""
    print("正在初始化代理...")
    graph = await initialize_agent()
    print("代理初始化完成！")
    
    # 测试数学计算
    print("\n测试数学计算...")
    config = {"configurable": {"thread_id": "test-thread"}}
    
    # 测试简单的数学问题
    result = await graph.ainvoke({
        "messages": [{"role": "user", "content": "请计算 15 + 25 的结果"}]
    }, config=config)
    
    print("用户: 请计算 15 + 25 的结果")
    print(f"助手: {result['messages'][-1].content}")
    
    # 测试复杂的数学问题
    print("\n测试复杂计算...")
    result = await graph.ainvoke({
        "messages": [{"role": "user", "content": "请计算 (10 + 5) * 3 - 8 的结果"}]
    }, config=config)
    
    print("用户: 请计算 (10 + 5) * 3 - 8 的结果")
    print(f"助手: {result['messages'][-1].content}")
    
    # 测试搜索功能
    print("\n测试搜索功能...")
    result = await graph.ainvoke({
        "messages": [{"role": "user", "content": "搜索一下今天的天气情况"}]
    }, config=config)
    
    print("用户: 搜索一下今天的天气情况")
    print(f"助手: {result['messages'][-1].content}")

if __name__ == "__main__":
    asyncio.run(test_agent())

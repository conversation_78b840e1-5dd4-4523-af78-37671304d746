#!/usr/bin/env python3
"""测试所有工具功能"""

import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from tutorial_quickstart.agent import initialize_agent

async def test_all_tools():
    """测试所有工具功能"""
    print("🚀 正在初始化代理...")
    graph = await initialize_agent()
    print("✅ 代理初始化完成！")
    
    config = {"configurable": {"thread_id": "test-all-tools"}}
    
    # 测试数学工具
    print("\n🧮 测试数学工具...")
    math_tests = [
        "计算 15 + 25",
        "计算 8 * 7", 
        "计算 100 - 35",
        "计算 84 除以 12"
    ]
    
    for test in math_tests:
        print(f"\n用户: {test}")
        result = await graph.ainvoke({
            "messages": [{"role": "user", "content": test}]
        }, config=config)
        print(f"助手: {result['messages'][-1].content}")
    
    # 测试天气工具
    print("\n🌤️ 测试天气工具...")
    weather_tests = [
        "查询北京的天气",
        "上海今天天气怎么样？",
        "广州的天气情况"
    ]
    
    for test in weather_tests:
        print(f"\n用户: {test}")
        try:
            result = await graph.ainvoke({
                "messages": [{"role": "user", "content": test}]
            }, config=config)
            print(f"助手: {result['messages'][-1].content}")
        except Exception as e:
            print(f"天气查询出错: {e}")
    
    # 测试搜索工具
    print("\n🔍 测试搜索工具...")
    search_tests = [
        "搜索最新的人工智能发展",
        "查找关于Python编程的信息"
    ]
    
    for test in search_tests:
        print(f"\n用户: {test}")
        try:
            result = await graph.ainvoke({
                "messages": [{"role": "user", "content": test}]
            }, config=config)
            content = result['messages'][-1].content
            # 截断长内容
            if len(content) > 200:
                content = content[:200] + "..."
            print(f"助手: {content}")
        except Exception as e:
            print(f"搜索出错: {e}")
    
    print("\n🎉 所有测试完成！")

if __name__ == "__main__":
    asyncio.run(test_all_tools())

"""Demo"""

import os
from dotenv import load_dotenv 
load_dotenv()

# pylint: disable=wrong-import-position
from fastapi import FastAPI
import uvicorn
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent
from tutorial_quickstart.agent import graph
from fastapi.staticfiles import StaticFiles

app = FastAPI()
sdk = CopilotKitRemoteEndpoint(
    agents=[
        LangGraphAgent(
            name="quickstart_agent",
            description="Quickstart agent.",
            graph=graph,
        ),
    ],
)

add_fastapi_endpoint(app, sdk, "/copilotkit")
tmp_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "tmp"))
os.makedirs(tmp_dir, exist_ok=True)
app.mount("/files", StaticFiles(directory=tmp_dir), name="files")

# add new route for health check
@app.get("/health")
def health():
    """Health check."""
    return {"status": "ok"}


def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run("tutorial_quickstart.demo:app", host="0.0.0.0", port=port)

if __name__ == "__main__":
    main()
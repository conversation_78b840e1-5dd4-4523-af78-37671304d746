"""MCP工具包装器 - 将MCP工具转换为LangChain工具"""

import json
import logging
from typing import Dict, Any, List, Optional, Type
from pydantic import BaseModel, create_model
from langchain_core.tools import BaseTool
from mcp import ClientSession
from mcp.types import Tool

logger = logging.getLogger(__name__)

class McpToolWrapper(BaseTool):
    """将MCP工具包装为LangChain工具"""
    
    name: str
    description: str
    mcp_client: ClientSession
    mcp_tool: Tool
    args_schema: Type[BaseModel] = None
    
    def __init__(self, mcp_client: ClientSession, mcp_tool: Tool):
        """初始化MCP工具包装器
        
        Args:
            mcp_client: MCP客户端
            mcp_tool: MCP工具
        """
        self.mcp_client = mcp_client
        self.mcp_tool = mcp_tool
        
        # 创建参数模式
        self.args_schema = self._create_args_schema(mcp_tool)
        
        super().__init__(
            name=mcp_tool.name,
            description=mcp_tool.description,
            args_schema=self.args_schema
        )
    
    def _create_args_schema(self, mcp_tool: Tool) -> Type[BaseModel]:
        """从MCP工具参数模式创建Pydantic模型
        
        Args:
            mcp_tool: MCP工具
            
        Returns:
            Pydantic模型类
        """
        try:
            # 解析参数模式
            schema = json.loads(mcp_tool.parameter_schema)
            
            # 提取属性
            properties = schema.get("properties", {})
            required = schema.get("required", [])
            
            # 创建字段定义
            field_definitions = {}
            for name, prop in properties.items():
                # 确定字段类型
                field_type = str
                if prop.get("type") == "number" or prop.get("type") == "integer":
                    field_type = float if prop.get("type") == "number" else int
                elif prop.get("type") == "boolean":
                    field_type = bool
                elif prop.get("type") == "array":
                    field_type = List[Any]
                elif prop.get("type") == "object":
                    field_type = Dict[str, Any]
                
                # 确定是否必填
                is_required = name in required
                
                # 添加字段定义
                field_definitions[name] = (
                    field_type if is_required else Optional[field_type],
                    ... if is_required else None
                )
            
            # 创建模型类
            model_name = f"{mcp_tool.name}Schema"
            model = create_model(model_name, **field_definitions)
            
            return model
            
        except Exception as e:
            logger.error(f"创建参数模式失败: {str(e)}")
            # 返回空模型
            return create_model(f"{mcp_tool.name}Schema")
    
    async def _arun(self, **kwargs) -> str:
        """异步运行工具

        Args:
            **kwargs: 工具参数

        Returns:
            工具执行结果
        """
        try:
            # 调用MCP工具
            result = await self.mcp_client.call_tool(
                name=self.mcp_tool.name,
                arguments=kwargs
            )

            # 返回结果
            if hasattr(result, 'content') and result.content:
                # 处理结果内容
                content_parts = []
                for content in result.content:
                    if hasattr(content, 'text'):
                        content_parts.append(content.text)
                    else:
                        content_parts.append(str(content))
                return '\n'.join(content_parts)
            else:
                return str(result)

        except Exception as e:
            logger.error(f"调用MCP工具失败: {str(e)}")
            return f"调用MCP工具失败: {str(e)}"

    def _run(self, **kwargs) -> str:
        """同步运行工具（为了兼容性）

        Args:
            **kwargs: 工具参数

        Returns:
            工具执行结果
        """
        import asyncio
        try:
            # 尝试获取当前事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果已经在事件循环中，创建一个任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._arun(**kwargs))
                    return future.result()
            else:
                # 如果没有运行的事件循环，直接运行
                return asyncio.run(self._arun(**kwargs))
        except Exception as e:
            logger.error(f"调用MCP工具失败: {str(e)}")
            return f"调用MCP工具失败: {str(e)}"